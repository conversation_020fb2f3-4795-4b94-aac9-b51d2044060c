'use client'

import { Queue } from '@/lib/types/queue'
import { formatDuration } from '@/lib/utils/format'

interface QueueStatsProps {
  queues: Queue[]
}

export function QueueStats({ queues }: QueueStatsProps) {
  const totalQueues = queues.length
  const publicQueues = queues.filter(q => q.isPublic).length
  const privateQueues = totalQueues - publicQueues
  
  const totalVideos = queues.reduce((total, queue) => 
    total + (queue.metadata.videoCount || queue.queueData.items.length), 0
  )
  
  const totalDuration = queues.reduce((total, queue) => 
    total + (queue.metadata.totalDuration || 0), 0
  )
  
  const averageQueueLength = totalQueues > 0 ? Math.round(totalVideos / totalQueues) : 0
  
  const totalViews = queues.reduce((total, queue) => 
    total + (queue.metadata.viewCount || 0), 0
  )

  const recentQueues = queues
    .filter(queue => {
      const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000)
      return queue.createdAt > weekAgo
    })
    .length

  if (totalQueues === 0) {
    return null
  }

  return (
    <div className="glassmorphism rounded-2xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Queue Statistics</h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Total Queues */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-2xl font-bold text-primary-400 mb-1">
            {totalQueues}
          </div>
          <div className="text-sm text-dark-300">Total Queues</div>
        </div>

        {/* Total Videos */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-2xl font-bold text-blue-400 mb-1">
            {totalVideos}
          </div>
          <div className="text-sm text-dark-300">Total Videos</div>
        </div>

        {/* Total Duration */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-2xl font-bold text-green-400 mb-1">
            {formatDuration(totalDuration)}
          </div>
          <div className="text-sm text-dark-300">Total Duration</div>
        </div>

        {/* Total Views */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-2xl font-bold text-purple-400 mb-1">
            {totalViews}
          </div>
          <div className="text-sm text-dark-300">Total Views</div>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
        {/* Public Queues */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-xl font-bold text-green-400 mb-1">
            {publicQueues}
          </div>
          <div className="text-sm text-dark-300">Public</div>
        </div>

        {/* Private Queues */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-xl font-bold text-gray-400 mb-1">
            {privateQueues}
          </div>
          <div className="text-sm text-dark-300">Private</div>
        </div>

        {/* Average Length */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-xl font-bold text-yellow-400 mb-1">
            {averageQueueLength}
          </div>
          <div className="text-sm text-dark-300">Avg Length</div>
        </div>

        {/* Recent Queues */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-xl font-bold text-orange-400 mb-1">
            {recentQueues}
          </div>
          <div className="text-sm text-dark-300">This Week</div>
        </div>
      </div>

      {/* Quick Insights */}
      <div className="mt-4 p-4 bg-dark-800/30 rounded-lg">
        <h4 className="text-sm font-medium text-white mb-2">Quick Insights</h4>
        <div className="space-y-1 text-sm text-dark-300">
          {totalQueues > 0 && (
            <div>
              • You have {totalQueues} queue{totalQueues > 1 ? 's' : ''} with {totalVideos} video{totalVideos > 1 ? 's' : ''}
            </div>
          )}
          {publicQueues > 0 && (
            <div>
              • {publicQueues} of your queues are public and shareable
            </div>
          )}
          {totalDuration > 0 && (
            <div>
              • Total watch time: {formatDuration(totalDuration)}
            </div>
          )}
          {recentQueues > 0 && (
            <div>
              • You created {recentQueues} queue{recentQueues > 1 ? 's' : ''} this week
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
