'use client'

import { <PERSON>actN<PERSON>, useState, useEffect } from 'react'
import { PasswordGateContext } from '@/hooks/usePasswordGate'

interface PasswordGateProviderProps {
  children: ReactNode
}

const STORAGE_KEY = 'tubli_password_verified'
const SESSION_DURATION = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

export function PasswordGateProvider({ children }: PasswordGateProviderProps) {
  const [isPasswordVerified, setIsPasswordVerified] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Check if password is already verified on mount
  useEffect(() => {
    const checkStoredPassword = () => {
      try {
        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const { timestamp } = JSON.parse(stored)
          const now = Date.now()
          
          // Check if the stored verification is still valid (within session duration)
          if (now - timestamp < SESSION_DURATION) {
            setIsPasswordVerified(true)
          } else {
            // Clear expired verification
            localStorage.removeItem(STORAGE_KEY)
          }
        }
      } catch (error) {
        console.error('Error checking stored password:', error)
        localStorage.removeItem(STORAGE_KEY)
      } finally {
        setIsLoading(false)
      }
    }

    checkStoredPassword()
  }, [])

  const verifyPassword = async (password: string): Promise<boolean> => {
    try {
      // Import Firebase dependencies dynamically to avoid SSR issues
      const { initializeApp, getApps } = await import('firebase/app')
      const { getFirestore, doc, getDoc } = await import('firebase/firestore')
      const bcrypt = await import('bcryptjs')

      // Firebase config from environment variables
      const firebaseConfig = {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
      }

      // Initialize Firebase
      let app
      if (getApps().length === 0) {
        app = initializeApp(firebaseConfig)
      } else {
        app = getApps()[0]
      }

      const db = getFirestore(app)

      // Get password from Firebase app_config collection
      const configRef = doc(db, 'app_config', 'access_control')
      const configSnap = await getDoc(configRef)

      if (!configSnap.exists()) {
        console.log('⚠️ No password configured in Firebase. Allowing access.')
        // Store verification with timestamp
        const verificationData = {
          verified: true,
          timestamp: Date.now()
        }

        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(verificationData))
        } catch (error) {
          console.error('Error storing password verification:', error)
        }

        setIsPasswordVerified(true)
        return true
      }

      const configData = configSnap.data()
      const storedPassword = configData?.password

      if (!storedPassword) {
        console.log('⚠️ No password field found in Firebase. Allowing access.')
        // Store verification with timestamp
        const verificationData = {
          verified: true,
          timestamp: Date.now()
        }

        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(verificationData))
        } catch (error) {
          console.error('Error storing password verification:', error)
        }

        setIsPasswordVerified(true)
        return true
      }

      // Verify password using bcrypt
      const isValid = await bcrypt.compare(password, storedPassword)
      console.log(`🔐 Password verification: ${isValid ? 'VALID' : 'INVALID'}`)

      if (isValid) {
        // Store verification with timestamp
        const verificationData = {
          verified: true,
          timestamp: Date.now()
        }

        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(verificationData))
        } catch (error) {
          console.error('Error storing password verification:', error)
        }

        setIsPasswordVerified(true)
        return true
      }

      return false
    } catch (error) {
      console.error('Error verifying password:', error)
      return false
    }
  }

  const clearPasswordAccess = () => {
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.error('Error clearing password access:', error)
    }
    setIsPasswordVerified(false)
  }

  const value = {
    isPasswordVerified,
    verifyPassword,
    clearPasswordAccess,
  }

  // Show loading state while checking stored password
  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <PasswordGateContext.Provider value={value}>
      {children}
    </PasswordGateContext.Provider>
  )
}
