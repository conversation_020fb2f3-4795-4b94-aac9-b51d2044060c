'use client'

import { useState, useRef } from 'react'
import { youtubeService } from '@/lib/services/youtube'
import { useDraftQueue } from '@/hooks/useDraftQueue'

interface SearchInputProps {
  onSearch: (query: string) => void
  isLoading?: boolean
  placeholder?: string
}

export function SearchInput({ onSearch, isLoading = false, placeholder = 'Search...' }: SearchInputProps) {
  const [query, setQuery] = useState('')
  const [isAddingVideo, setIsAddingVideo] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const { addToDraft, isCreationMode, isEditMode } = useDraftQueue()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!query.trim() || isLoading || isAddingVideo) return

    // Check if the query is a YouTube URL
    if (youtubeService.isValidYouTubeUrl(query.trim())) {
      await handleYouTubeUrl(query.trim())
    } else {
      onSearch(query.trim())
    }
  }

  const handleYouTubeUrl = async (url: string) => {
    if (!isCreationMode && !isEditMode) {
      console.warn('Not in creation or edit mode, cannot add video directly')
      return
    }

    setIsAddingVideo(true)
    try {
      const videoId = youtubeService.extractVideoId(url)
      if (!videoId) {
        console.error('Could not extract video ID from URL:', url)
        return
      }

      console.log('🔗 Adding YouTube video directly from URL (with API):', videoId)

      // Get full metadata from YouTube API
      const videoMetadata = await youtubeService.getVideoMetadata(videoId)
      if (!videoMetadata) {
        console.error('Could not fetch video metadata from YouTube API')
        return
      }

      if (videoMetadata) {
        const draftId = addToDraft(videoMetadata)
        if (draftId) {
          console.log('✅ Added video from URL to draft queue:', videoMetadata.title, 'Draft ID:', draftId)
          setQuery('') // Clear the input after successful addition
        }
      } else {
        console.error('Could not create video metadata for:', videoId)
      }
    } catch (error) {
      console.error('❌ Failed to add video from URL:', error)
    } finally {
      setIsAddingVideo(false)
    }
  }

  const handleClear = () => {
    setQuery('')
    onSearch('')
    inputRef.current?.focus()
  }

  const isProcessing = isLoading || isAddingVideo

  return (
    <form onSubmit={handleSubmit} className="relative">
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={placeholder}
          disabled={isProcessing}
          className="input-field pr-24 pl-12"
        />
        
        {/* Search Icon */}
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-dark-400">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
        </div>

        {/* Clear Button */}
        {query && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-24 top-1/2 transform -translate-y-1/2 p-1 rounded-full text-dark-400 hover:text-white hover:bg-white/10 transition-colors duration-200"
            title="Clear search"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        )}

        {/* Search Button */}
        <button
          type="submit"
          disabled={!query.trim() || isProcessing}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary px-3 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? (
            <div className="loading-spinner w-4 h-4"></div>
          ) : youtubeService.isValidYouTubeUrl(query.trim()) ? (
            'Add'
          ) : (
            'Search'
          )}
        </button>
      </div>
    </form>
  )
}
