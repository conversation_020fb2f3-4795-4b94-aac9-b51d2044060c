'use client'

import { useState, useEffect, useCallback } from 'react'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { useNavigation } from '@/hooks/useNavigation'
import { useAuth } from '@/hooks/useAuth'
import { firebaseService } from '@/lib/services/firebase'

interface QueueCreationFormProps {
  onSuccess?: (queueId: string) => void
}

export function QueueCreationForm({ onSuccess }: QueueCreationFormProps) {
  const { user } = useAuth()
  const {
    draftCount,
    draftDuration,
    isCreationMode, 
    isEditMode,
    editingQueueId,
    exitCreationMode, 
    exitEditMode,
    saveDraftAsQueue,
    updateExistingQueue
  } = useDraftQueue()
  
  const { setActiveView } = useNavigation()
  
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [tags, setTags] = useState('')
  const [queueLoopCount, setQueueLoopCount] = useState(-1) // -1 = infinite, 0+ = specific count
  const [isPublic, setIsPublic] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const loadQueueDataForEdit = useCallback(async () => {
    if (!editingQueueId) return

    try {
      const queue = await firebaseService.getQueue(editingQueueId, user?.uid)
      if (queue) {
        setTitle(queue.metadata.title || '')
        setDescription(queue.metadata.description || '')
        setTags(queue.metadata.tags?.join(', ') || '')
        setQueueLoopCount(queue.queueData.queueLoopCount !== undefined ? queue.queueData.queueLoopCount : -1)
        // Note: isPublic is not editable in edit mode, so we don't set it
      }
    } catch (error) {
      console.error('Failed to load queue data for editing:', error)
    }
  }, [editingQueueId, user?.uid])

  // Reset form when entering creation mode or load data when entering edit mode
  useEffect(() => {
    if (isCreationMode && !isEditMode) {
      setTitle('')
      setDescription('')
      setTags('')
      setQueueLoopCount(-1) // Default to infinite
      setIsPublic(false)
    } else if (isEditMode && editingQueueId) {
      // Load the queue data to populate the form
      loadQueueDataForEdit()
    }
  }, [isCreationMode, isEditMode, editingQueueId, loadQueueDataForEdit])



  const handleSave = async () => {
    if (!title.trim() || draftCount === 0 || isSaving) return

    setIsSaving(true)
    try {
      let success = false
      let queueId: string | null = null

      if (isEditMode && editingQueueId) {
        // Update existing queue
        const tagsArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
        success = await updateExistingQueue(title.trim(), description.trim(), tagsArray, queueLoopCount)
        queueId = editingQueueId
      } else {
        // Create new queue
        queueId = await saveDraftAsQueue(title.trim(), isPublic, queueLoopCount)
        success = queueId !== null
      }

      if (success && queueId) {
        console.log(`✅ Queue ${isEditMode ? 'updated' : 'created'} successfully:`, queueId)
        
        // Exit the appropriate mode
        if (isEditMode) {
          exitEditMode()
        } else {
          exitCreationMode()
        }
        
        // Navigate to personal queues view
        setActiveView('personal')
        
        // Call success callback
        onSuccess?.(queueId)
      } else {
        console.error(`Failed to ${isEditMode ? 'update' : 'create'} queue`)
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} queue:`, error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    if (isEditMode) {
      exitEditMode()
    } else {
      exitCreationMode()
    }
    setActiveView('personal')
  }

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    } else {
      return `${remainingSeconds}s`
    }
  }

  if (!isCreationMode && !isEditMode) {
    return null
  }

  return (
    <div className="glassmorphism rounded-2xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-white mb-2">
            {isEditMode ? 'Edit Queue' : 'Create New Queue'}
          </h2>
          <p className="text-dark-300">
            {isEditMode 
              ? 'Update your queue details and videos'
              : 'Save your draft queue with a title and description'
            }
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-white font-medium">{draftCount} videos</p>
          <p className="text-xs text-dark-400">{formatDuration(draftDuration)}</p>
        </div>
      </div>

      <div className="space-y-4">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Queue Title *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-4 py-3 bg-dark-800 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500"
            placeholder="Enter a title for your queue..."
            maxLength={100}
          />
          <p className="text-xs text-dark-400 mt-1">{title.length}/100 characters</p>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Description
          </label>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={3}
            className="w-full px-4 py-3 bg-dark-800 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500 resize-none"
            placeholder="Describe your queue (optional)..."
            maxLength={500}
          />
          <p className="text-xs text-dark-400 mt-1">{description.length}/500 characters</p>
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Tags
          </label>
          <input
            type="text"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            className="w-full px-4 py-3 bg-dark-800 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500"
            placeholder="music, rock, favorites (comma-separated)..."
          />
          <p className="text-xs text-dark-400 mt-1">Separate tags with commas</p>
        </div>

        {/* Queue Loop Count */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Queue Loop Count
          </label>
          <div className="flex items-center gap-3">
            <div className="flex-1">
              <select
                value={queueLoopCount === -1 ? 'infinite' : 'specific'}
                onChange={(e) => {
                  if (e.target.value === 'infinite') {
                    setQueueLoopCount(-1)
                  } else {
                    setQueueLoopCount(1)
                  }
                }}
                className="w-full px-4 py-3 bg-dark-800 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500"
              >
                <option value="infinite">Infinite (loop forever)</option>
                <option value="specific">Specific number</option>
              </select>
            </div>
            {queueLoopCount !== -1 && (
              <div className="w-24">
                <input
                  type="number"
                  min="1"
                  max="999"
                  value={queueLoopCount}
                  onChange={(e) => setQueueLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
                  className="w-full px-3 py-3 bg-dark-800 border border-white/10 rounded-lg text-white text-center focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500"
                  placeholder="1"
                />
              </div>
            )}
          </div>
          <p className="text-xs text-dark-400 mt-1">
            {queueLoopCount === -1
              ? 'Queue will loop infinitely until manually stopped'
              : `Queue will play ${queueLoopCount} time${queueLoopCount > 1 ? 's' : ''} then stop`
            }
          </p>
        </div>

        {/* Public Toggle (only for creation mode) */}
        {!isEditMode && (
          <div className="flex items-center justify-between p-4 bg-dark-800/50 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-white mb-1">Make Public</h4>
              <p className="text-xs text-dark-400">Allow others to discover and play your queue</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-500/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t border-white/10">
          <button
            onClick={handleCancel}
            className="px-6 py-3 text-dark-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!title.trim() || draftCount === 0 || isSaving}
            className="btn-primary px-6 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? (
              <div className="flex items-center gap-2">
                <div className="loading-spinner w-4 h-4"></div>
                {isEditMode ? 'Updating...' : 'Creating...'}
              </div>
            ) : (
              isEditMode ? 'Update Queue' : 'Create Queue'
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
